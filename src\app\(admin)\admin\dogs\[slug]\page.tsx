'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface Photo {
  id: string;
  url: string;
  title: string | null;
  description: string | null;
  isMain: boolean;
  order: number;
}

interface Dog {
  id: string;
  name: string;
  breed: string;
  gender: 'MALE' | 'FEMALE';
  birthDate: string;
  color: string | null;
  weight: number | null;
  height: number | null;
  pedigree: string | null;
  achievements: string | null;
  description: string | null;
  slug: string;
  isForSale: boolean;
  price: number | null;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string;
  photos: Photo[];
}

export default function DogDetailsPage({ params }: { params: { slug: string } }) {
  const [dog, setDog] = useState<Dog | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchDog = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/dogs/${params.slug}`);
        
        if (!response.ok) {
          throw new Error('Ошибка при загрузке данных');
        }
        
        const data = await response.json();
        setDog(data);
      } catch (err) {
        setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
        console.error('Ошибка при загрузке собаки:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDog();
  }, [params.slug]);

  const handleDelete = async () => {
    setIsDeleting(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/dogs/${params.slug}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Ошибка при удалении собаки');
      }
      
      router.push('/admin/dogs');
    } catch (err) {
      setError('Произошла ошибка при удалении собаки. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при удалении собаки:', err);
      setIsDeleting(false);
    }
  };

  const getGenderText = (gender: 'MALE' | 'FEMALE') => {
    return gender === 'MALE' ? 'Кобель' : 'Сука';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU');
  };

  const formatPrice = (price: number | null) => {
    if (price === null) return '-';
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
    }).format(price);
  };

  if (isLoading) {
    return (
      <AdminLayout title="Загрузка...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error || !dog) {
    return (
      <AdminLayout title="Ошибка">
        <Alert type="error">{error || 'Собака не найдена'}</Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/admin/dogs')}>
            Вернуться к списку собак
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={dog.name}>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            {dog.breed} • {getGenderText(dog.gender)}
          </p>
        </div>
        <div className="flex space-x-4">
          <Link href={`/admin/dogs/${dog.slug}/edit`}>
            <Button variant="secondary">Редактировать</Button>
          </Link>
          <Button
            variant="danger"
            onClick={() => setShowDeleteConfirm(true)}
          >
            Удалить
          </Button>
        </div>
      </div>

      {showDeleteConfirm && (
        <div className="mb-6">
          <Alert type="warning" title="Подтверждение удаления" onClose={() => setShowDeleteConfirm(false)}>
            <p className="mb-4">
              Вы уверены, что хотите удалить собаку "{dog.name}"? Это действие нельзя отменить.
            </p>
            <div className="flex justify-end space-x-4">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowDeleteConfirm(false)}
              >
                Отмена
              </Button>
              <Button
                variant="danger"
                size="sm"
                isLoading={isDeleting}
                onClick={handleDelete}
              >
                Удалить
              </Button>
            </div>
          </Alert>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            {dog.photos.length > 0 ? (
              <div className="relative aspect-square">
                <img
                  src={dog.photos.find(photo => photo.isMain)?.url || dog.photos[0].url}
                  alt={dog.name}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="aspect-square bg-gray-200 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-24 w-24 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
            )}

            {dog.photos.length > 1 && (
              <div className="p-4 grid grid-cols-4 gap-2">
                {dog.photos.map((photo) => (
                  <div
                    key={photo.id}
                    className={`aspect-square rounded-md overflow-hidden ${
                      photo.isMain ? 'ring-2 ring-blue-500' : ''
                    }`}
                  >
                    <img
                      src={photo.url}
                      alt={photo.title || dog.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="mt-6 bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Статус</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Опубликовано</p>
                <p>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      dog.isPublished
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {dog.isPublished ? 'Да' : 'Нет'}
                  </span>
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">На продажу</p>
                <p>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      dog.isForSale
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {dog.isForSale ? 'Да' : 'Нет'}
                  </span>
                </p>
              </div>
              {dog.isForSale && dog.price !== null && (
                <div>
                  <p className="text-sm text-gray-500">Цена</p>
                  <p className="font-medium">{formatPrice(dog.price)}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="md:col-span-2 space-y-6">
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Основная информация</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Имя</p>
                <p className="font-medium">{dog.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Порода</p>
                <p className="font-medium">{dog.breed}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Пол</p>
                <p className="font-medium">{getGenderText(dog.gender)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Дата рождения</p>
                <p className="font-medium">{formatDate(dog.birthDate)}</p>
              </div>
              {dog.color && (
                <div>
                  <p className="text-sm text-gray-500">Окрас</p>
                  <p className="font-medium">{dog.color}</p>
                </div>
              )}
              {dog.weight !== null && (
                <div>
                  <p className="text-sm text-gray-500">Вес</p>
                  <p className="font-medium">{dog.weight} кг</p>
                </div>
              )}
              {dog.height !== null && (
                <div>
                  <p className="text-sm text-gray-500">Рост</p>
                  <p className="font-medium">{dog.height} см</p>
                </div>
              )}
              {dog.pedigree && (
                <div>
                  <p className="text-sm text-gray-500">Родословная</p>
                  <p className="font-medium">{dog.pedigree}</p>
                </div>
              )}
            </div>
          </div>

          {(dog.achievements || dog.description) && (
            <div className="bg-white shadow-md rounded-lg p-6">
              <h2 className="text-lg font-medium mb-4">Дополнительная информация</h2>
              {dog.achievements && (
                <div className="mb-4">
                  <p className="text-sm text-gray-500 mb-2">Достижения</p>
                  <p className="whitespace-pre-line">{dog.achievements}</p>
                </div>
              )}
              {dog.description && (
                <div>
                  <p className="text-sm text-gray-500 mb-2">Описание</p>
                  <p className="whitespace-pre-line">{dog.description}</p>
                </div>
              )}
            </div>
          )}

          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Системная информация</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">ID</p>
                <p className="font-mono text-sm">{dog.id}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Slug</p>
                <p className="font-mono text-sm">{dog.slug}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Создано</p>
                <p className="font-mono text-sm">{formatDate(dog.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Обновлено</p>
                <p className="font-mono text-sm">{formatDate(dog.updatedAt)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
