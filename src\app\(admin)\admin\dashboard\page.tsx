'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import { JSX, useEffect, useState } from 'react';
import Link from 'next/link';
import Alert from '@/components/ui/Alert';

interface DashboardStats {
  dogsCount: number;
  puppiesCount: number;
  breedingsCount: number;
  galleryCount: number;
  newsCount: number;
  inquiriesCount: number;
  newInquiriesCount: number;
}

export default function AdminDashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    dogsCount: 0,
    puppiesCount: 0,
    breedingsCount: 0,
    galleryCount: 0,
    newsCount: 0,
    inquiriesCount: 0,
    newInquiriesCount: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Получаем количество собак
        const dogsResponse = await fetch('/api/dogs');
        const dogsData = await dogsResponse.json();

        // Получаем количество щенков
        const puppiesResponse = await fetch('/api/puppies');
        const puppiesData = await puppiesResponse.json();

        // Получаем количество заявок
        const inquiriesResponse = await fetch('/api/inquiries');
        const inquiriesData = await inquiriesResponse.json();

        // Получаем количество новостей
        const newsResponse = await fetch('/api/news');
        const newsData = await newsResponse.json();

        // Получаем количество альбомов галереи
        const galleryResponse = await fetch('/api/gallery');
        const galleryData = await galleryResponse.json();

        setStats({
          dogsCount: Array.isArray(dogsData) ? dogsData.length : 0,
          puppiesCount: Array.isArray(puppiesData) ? puppiesData.length : 0,
          breedingsCount: 0, // Пока нет API для вязок
          galleryCount: Array.isArray(galleryData) ? galleryData.length : 0,
          newsCount: Array.isArray(newsData.news) ? newsData.news.length : 0,
          inquiriesCount: Array.isArray(inquiriesData.inquiries) ? inquiriesData.inquiries.length : 0,
          newInquiriesCount: Array.isArray(inquiriesData.inquiries)
            ? inquiriesData.inquiries.filter((inquiry: any) => inquiry.status === 'NEW').length
            : 0,
        });
      } catch (err) {
        console.error('Ошибка при загрузке статистики:', err);
        setError('Произошла ошибка при загрузке статистики. Некоторые данные могут быть недоступны.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  const StatCard = ({
    title,
    count,
    icon,
    href,
    color = 'blue',
    loading = false
  }: {
    title: string;
    count: number;
    icon: JSX.Element;
    href: string;
    color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
    loading?: boolean;
  }) => {
    const colorClasses = {
      blue: {
        border: 'border-blue-200',
        text: 'text-blue-700',
        bg: 'bg-blue-100',
        hover: 'hover:border-blue-300'
      },
      green: {
        border: 'border-green-200',
        text: 'text-green-700',
        bg: 'bg-green-100',
        hover: 'hover:border-green-300'
      },
      yellow: {
        border: 'border-yellow-200',
        text: 'text-yellow-700',
        bg: 'bg-yellow-100',
        hover: 'hover:border-yellow-300'
      },
      red: {
        border: 'border-red-200',
        text: 'text-red-700',
        bg: 'bg-red-100',
        hover: 'hover:border-red-300'
      },
      purple: {
        border: 'border-purple-200',
        text: 'text-purple-700',
        bg: 'bg-purple-100',
        hover: 'hover:border-purple-300'
      },
    };

    return (
      <Link href={href} className="block">
        <div className={`bg-white shadow-sm rounded-md transition-all duration-200 h-full border ${colorClasses[color].border} ${colorClasses[color].hover}`}>
          <div className="p-5">
            <div className="flex items-center">
              <div className={`flex-shrink-0 mr-4 w-10 h-10 ${colorClasses[color].bg} rounded-md flex items-center justify-center`}>
                <div className={`${colorClasses[color].text}`}>
                  {icon}
                </div>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{title}</h3>
                {loading ? (
                  <div className="h-5 w-16 bg-gray-200 animate-pulse rounded mt-1"></div>
                ) : (
                  <p className="text-xl font-semibold text-gray-800 mt-1">{count}</p>
                )}
              </div>
            </div>
          </div>
          <div className="border-t border-gray-100 px-5 py-3 flex justify-end">
            <span className="text-sm text-gray-500 flex items-center">
              Управление
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 ml-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </span>
          </div>
        </div>
      </Link>
    );
  };

  return (
    <AdminLayout title="Панель управления">
      {error && (
        <div className="mb-6">
          <Alert type="warning" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatCard
          title="Собаки"
          count={stats.dogsCount}
          loading={isLoading}
          href="/admin/dogs"
          color="blue"
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15.182 15.182a4.5 4.5 0 01-6.364 0M21 12a9 9 0 11-18 0 9 9 0 0118 0zM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75zm-.375 0h.008v.015h-.008V9.75zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75zm-.375 0h.008v.015h-.008V9.75z"
              />
            </svg>
          }
        />

        <StatCard
          title="Щенки"
          count={stats.puppiesCount}
          loading={isLoading}
          href="/admin/puppies"
          color="green"
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15.182 16.318A4.486 4.486 0 0012.016 15a4.486 4.486 0 00-3.198 1.318M21 12a9 9 0 11-18 0 9 9 0 0118 0zM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75zm-.375 0h.008v.015h-.008V9.75zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75zm-.375 0h.008v.015h-.008V9.75z"
              />
            </svg>
          }
        />

        <StatCard
          title="Вязки"
          count={stats.breedingsCount}
          loading={isLoading}
          href="/admin/breedings"
          color="purple"
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z"
              />
            </svg>
          }
        />

        <StatCard
          title="Галерея"
          count={stats.galleryCount}
          loading={isLoading}
          href="/admin/gallery"
          color="blue"
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"
              />
            </svg>
          }
        />

        <StatCard
          title="Новости"
          count={stats.newsCount}
          loading={isLoading}
          href="/admin/news"
          color="green"
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 01-2.25 2.25M16.5 7.5V18a2.25 2.25 0 002.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 002.25 2.25h13.5M6 7.5h3v3H6v-3z"
              />
            </svg>
          }
        />

        <StatCard
          title="Заявки"
          count={stats.inquiriesCount}
          loading={isLoading}
          href="/admin/inquiries"
          color={stats.newInquiriesCount > 0 ? 'red' : 'blue'}
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75"
              />
            </svg>
          }
        />
      </div>

      {stats.newInquiriesCount > 0 && (
        <div className="mt-6">
          <Alert type="info">
            <p>
              У вас есть <strong>{stats.newInquiriesCount}</strong> {stats.newInquiriesCount === 1 ? 'новая заявка' : 'новых заявок'}.{' '}
              <Link href="/admin/inquiries" className="font-medium text-blue-600 hover:text-blue-500">
                Перейти к заявкам
              </Link>
            </p>
          </Alert>
        </div>
      )}

      <div className="mt-12">
        <h2 className="text-lg font-semibold mb-5">Быстрые действия</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Link href="/admin/dogs/create" className="block">
            <div className="bg-white rounded-md shadow-sm hover:shadow transition-all duration-200 group h-full border border-gray-200">
              <div className="p-5 flex items-center">
                <div className="flex-shrink-0 mr-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-md flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-blue-700"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4v16m8-8H4"
                      />
                    </svg>
                  </div>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Добавить собаку</h3>
                  <p className="text-sm text-gray-500 mt-1">Создать карточку собаки</p>
                </div>
              </div>
            </div>
          </Link>

          <Link href="/admin/puppies/create" className="block">
            <div className="bg-white rounded-md shadow-sm hover:shadow transition-all duration-200 group h-full border border-gray-200">
              <div className="p-5 flex items-center">
                <div className="flex-shrink-0 mr-4">
                  <div className="w-10 h-10 bg-green-100 rounded-md flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-green-700"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4v16m8-8H4"
                      />
                    </svg>
                  </div>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Добавить щенка</h3>
                  <p className="text-sm text-gray-500 mt-1">Создать карточку щенка</p>
                </div>
              </div>
            </div>
          </Link>

          <Link href="/admin/news/create" className="block">
            <div className="bg-white rounded-md shadow-sm hover:shadow transition-all duration-200 group h-full border border-gray-200">
              <div className="p-5 flex items-center">
                <div className="flex-shrink-0 mr-4">
                  <div className="w-10 h-10 bg-yellow-100 rounded-md flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-yellow-700"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4v16m8-8H4"
                      />
                    </svg>
                  </div>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Добавить новость</h3>
                  <p className="text-sm text-gray-500 mt-1">Создать публикацию</p>
                </div>
              </div>
            </div>
          </Link>

          <Link href="/admin/gallery/create" className="block">
            <div className="bg-white rounded-md shadow-sm hover:shadow transition-all duration-200 group h-full border border-gray-200">
              <div className="p-5 flex items-center">
                <div className="flex-shrink-0 mr-4">
                  <div className="w-10 h-10 bg-purple-100 rounded-md flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-purple-700"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4v16m8-8H4"
                      />
                    </svg>
                  </div>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Добавить альбом</h3>
                  <p className="text-sm text-gray-500 mt-1">Создать фотоальбом</p>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </div>

      <div className="mt-12">
        <h2 className="text-lg font-semibold mb-5">Полезные ссылки</h2>
        <div className="bg-white shadow-sm rounded-md border border-gray-200">
          <div className="divide-y divide-gray-200">
            <Link href="/" className="block hover:bg-gray-50 transition-colors">
              <div className="px-6 py-4 flex items-center">
                <div className="flex-shrink-0 mr-4">
                  <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 text-blue-700"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                      />
                    </svg>
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">Перейти на сайт</h3>
                  <p className="text-sm text-gray-500 mt-1">Просмотр публичной части сайта</p>
                </div>
                <div className="text-gray-400">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>

            <Link href="/admin/settings" className="block hover:bg-gray-50 transition-colors">
              <div className="px-6 py-4 flex items-center">
                <div className="flex-shrink-0 mr-4">
                  <div className="w-8 h-8 bg-gray-100 rounded-md flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 text-gray-700"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">Настройки сайта</h3>
                  <p className="text-sm text-gray-500 mt-1">Управление настройками сайта</p>
                </div>
                <div className="text-gray-400">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>

            <Link href="/admin/inquiries" className="block hover:bg-gray-50 transition-colors">
              <div className="px-6 py-4 flex items-center">
                <div className="flex-shrink-0 mr-4">
                  <div className="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 text-red-700"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">Заявки</h3>
                  <p className="text-sm text-gray-500 mt-1">Просмотр и обработка заявок</p>
                </div>
                <div className="text-gray-400">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
