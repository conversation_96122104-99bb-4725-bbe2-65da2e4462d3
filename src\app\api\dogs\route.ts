import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/dogs - Получение списка собак
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const gender = searchParams.get('gender');
    const isForSale = searchParams.get('isForSale');
    
    // Формируем условия фильтрации
    const where: any = { isPublished: true };
    
    if (gender) {
      where.gender = gender;
    }
    
    if (isForSale) {
      where.isForSale = isForSale === 'true';
    }
    
    const dogs = await prisma.dog.findMany({
      where,
      include: {
        photos: {
          where: { isMain: true },
          take: 1
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return NextResponse.json(dogs);
  } catch (error) {
    console.error('Ошибка при получении списка собак:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении списка собак' },
      { status: 500 }
    );
  }
}

// POST /api/dogs - Создание новой собаки (защищенный маршрут)
export async function POST(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const data = await request.json();
    
    // Создаем новую собаку
    const dog = await prisma.dog.create({
      data: {
        name: data.name,
        breed: data.breed,
        gender: data.gender,
        birthDate: new Date(data.birthDate),
        color: data.color,
        weight: data.weight ? parseFloat(data.weight) : null,
        height: data.height ? parseFloat(data.height) : null,
        pedigree: data.pedigree,
        achievements: data.achievements,
        description: data.description,
        slug: data.slug,
        isForSale: data.isForSale || false,
        price: data.price ? parseFloat(data.price) : null,
        isPublished: data.isPublished || true,
      }
    });
    
    return NextResponse.json(dog, { status: 201 });
  } catch (error) {
    console.error('Ошибка при создании собаки:', error);
    return NextResponse.json(
      { error: 'Ошибка при создании собаки' },
      { status: 500 }
    );
  }
}
