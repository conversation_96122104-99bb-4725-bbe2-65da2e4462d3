'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { PawPrint } from '@/components/ui/Decorations';

interface HeroSlide {
  id: string;
  title?: string;
  subtitle?: string;
  imageUrl: string;
  buttonText?: string;
  buttonLink?: string;
  order: number;
  isActive: boolean;
}

interface HeroSliderProps {
  slides: HeroSlide[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

export default function HeroSlider({ 
  slides, 
  autoPlay = true, 
  autoPlayInterval = 5000 
}: HeroSliderProps) {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Фильтруем только активные слайды и сортируем по порядку
  const activeSlides = slides
    .filter(slide => slide.isActive)
    .sort((a, b) => a.order - b.order);

  // Автопроигрывание
  useEffect(() => {
    if (!autoPlay || activeSlides.length <= 1) return;

    const interval = setInterval(() => {
      nextSlide();
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [currentSlide, autoPlay, autoPlayInterval, activeSlides.length]);

  const nextSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentSlide((prev) => (prev + 1) % activeSlides.length);
    setTimeout(() => setIsTransitioning(false), 500);
  };

  const prevSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentSlide((prev) => (prev - 1 + activeSlides.length) % activeSlides.length);
    setTimeout(() => setIsTransitioning(false), 500);
  };

  const goToSlide = (index: number) => {
    if (isTransitioning || index === currentSlide) return;
    setIsTransitioning(true);
    setCurrentSlide(index);
    setTimeout(() => setIsTransitioning(false), 500);
  };

  // Если нет активных слайдов, показываем заглушку
  if (activeSlides.length === 0) {
    return (
      <section className="relative bg-forest-dark text-white min-h-[90vh] flex items-center">
        <div className="absolute inset-0 bg-gradient-to-r from-forest-dark to-forest-medium"></div>
        <div className="container mx-auto px-4 py-24 relative z-20 text-center">
          <PawPrint size="lg" className="mx-auto mb-6 text-shiba-orange" />
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            Добро пожаловать в наш питомник
          </h1>
          <p className="text-xl mb-10 text-white/90">
            Настройте слайды в админ-панели для создания красивой презентации
          </p>
        </div>
      </section>
    );
  }

  const currentSlideData = activeSlides[currentSlide];

  return (
    <section className="relative bg-forest-dark text-white min-h-[90vh] flex items-center overflow-hidden">
      {/* Слайды */}
      <div className="absolute inset-0">
        {activeSlides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-opacity duration-500 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <Image
              src={slide.imageUrl}
              alt={slide.title || 'Hero slide'}
              fill
              style={{ objectFit: 'cover', objectPosition: 'center' }}
              priority={index === 0}
            />
            {/* Затенение */}
            <div className="absolute inset-0 bg-black/40"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-forest-dark/70 to-forest-medium/50"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
          </div>
        ))}
      </div>

      {/* Контент слайда */}
      <div className="container mx-auto px-4 py-24 relative z-20">
        <div className="max-w-2xl">
          <div className="flex items-center mb-4">
            <PawPrint size="md" className="mr-3" />
            <span className="text-shiba-orange font-medium tracking-wider">
              {currentSlideData.subtitle || 'ПИТОМНИК СОБАК ПОРОДЫ СИБА-ИНУ'}
            </span>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
            {currentSlideData.title || (
              <>
                Верный друг и <span className="text-shiba-orange">преданный компаньон</span>
              </>
            )}
          </h1>
          
          <p className="text-xl mb-10 text-white/90 leading-relaxed">
            Мы занимаемся разведением и продажей породистых собак высшего качества.
            Наши питомцы выращены с любовью и заботой, чтобы стать идеальным членом вашей семьи.
          </p>
          
          <div className="flex flex-wrap gap-4">
            {currentSlideData.buttonText && currentSlideData.buttonLink ? (
              <Link
                href={currentSlideData.buttonLink}
                className="btn-shiba px-8 py-4 rounded-lg font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center"
              >
                <span>{currentSlideData.buttonText}</span>
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
              </Link>
            ) : (
              <>
                <Link
                  href="/dogs"
                  className="btn-shiba px-8 py-4 rounded-lg font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center"
                >
                  <span>Наши собаки</span>
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                  </svg>
                </Link>
                <Link
                  href="/puppies"
                  className="btn-outline-shiba px-8 py-4 rounded-lg font-semibold transition-all duration-300 flex items-center"
                >
                  <span>Щенки на продажу</span>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Навигация слайдера */}
      {activeSlides.length > 1 && (
        <>
          {/* Стрелки */}
          <button
            onClick={prevSlide}
            disabled={isTransitioning}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 z-30 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300 disabled:opacity-50"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          
          <button
            onClick={nextSlide}
            disabled={isTransitioning}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 z-30 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300 disabled:opacity-50"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>

          {/* Индикаторы */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 flex space-x-2">
            {activeSlides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                disabled={isTransitioning}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? 'bg-shiba-orange scale-125'
                    : 'bg-white/50 hover:bg-white/70'
                }`}
              />
            ))}
          </div>
        </>
      )}
    </section>
  );
}
