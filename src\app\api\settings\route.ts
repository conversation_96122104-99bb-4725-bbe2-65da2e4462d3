import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/settings - Получение настроек сайта
export async function GET(request: NextRequest) {
  try {
    // Получаем все настройки сайта
    const settingsRecords = await prisma.settings.findMany();

    // Если настройки не найдены, создаем их с дефолтными значениями
    if (settingsRecords.length === 0) {
      const defaultSettings = [
        { key: 'siteName', value: 'Питомник собак', description: 'Название сайта' },
        { key: 'siteDescription', value: 'Официальный сайт питомника собак', description: 'Описание сайта' },
        { key: 'contactEmail', value: '<EMAIL>', description: 'Контактный email' },
        { key: 'contactPhone', value: '+7 (999) 123-45-67', description: 'Контактный телефон' },
      ];

      for (const setting of defaultSettings) {
        await prisma.settings.create({
          data: setting
        });
      }

      // Получаем созданные настройки
      const newSettingsRecords = await prisma.settings.findMany();
      settingsRecords.push(...newSettingsRecords);
    }

    // Преобразуем записи в объект для удобства использования
    const settings = settingsRecords.reduce((acc, record) => {
      acc[record.key] = record.value;
      return acc;
    }, {} as Record<string, string>);

    return NextResponse.json(settings);
  } catch (error) {
    console.error('Ошибка при получении настроек сайта:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении настроек сайта' },
      { status: 500 }
    );
  }
}

// PUT /api/settings - Обновление настроек сайта (защищенный маршрут)
export async function PUT(request: NextRequest) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Обновляем каждую настройку
    for (const [key, value] of Object.entries(data)) {
      if (value === null || value === undefined) continue;

      // Проверяем, существует ли настройка
      const existingSetting = await prisma.settings.findUnique({
        where: { key }
      });

      if (existingSetting) {
        // Обновляем существующую настройку
        await prisma.settings.update({
          where: { key },
          data: { value: String(value) }
        });
      } else {
        // Создаем новую настройку
        await prisma.settings.create({
          data: {
            key,
            value: String(value),
            description: `Настройка ${key}`
          }
        });
      }
    }

    // Получаем обновленные настройки
    const settingsRecords = await prisma.settings.findMany();

    // Преобразуем записи в объект для удобства использования
    const settings = settingsRecords.reduce((acc, record) => {
      acc[record.key] = record.value;
      return acc;
    }, {} as Record<string, string>);

    return NextResponse.json(settings);
  } catch (error) {
    console.error('Ошибка при обновлении настроек сайта:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении настроек сайта' },
      { status: 500 }
    );
  }
}
