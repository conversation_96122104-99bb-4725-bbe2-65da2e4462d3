import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// GET /api/dogs/[slug] - Получение информации о конкретной собаке
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug;
    
    const dog = await prisma.dog.findUnique({
      where: { slug },
      include: {
        photos: {
          orderBy: {
            order: 'asc'
          }
        },
        breedings: {
          include: {
            puppies: true
          }
        }
      }
    });
    
    if (!dog) {
      return NextResponse.json(
        { error: 'Собака не найдена' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(dog);
  } catch (error) {
    console.error('Ошибка при получении информации о собаке:', error);
    return NextResponse.json(
      { error: 'Ошибка при получении информации о собаке' },
      { status: 500 }
    );
  }
}

// PUT /api/dogs/[slug] - Обновление информации о собаке (защищенный маршрут)
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const slug = params.slug;
    const data = await request.json();
    
    // Проверяем, существует ли собака
    const existingDog = await prisma.dog.findUnique({
      where: { slug }
    });
    
    if (!existingDog) {
      return NextResponse.json(
        { error: 'Собака не найдена' },
        { status: 404 }
      );
    }
    
    // Обновляем информацию о собаке
    const updatedDog = await prisma.dog.update({
      where: { slug },
      data: {
        name: data.name,
        breed: data.breed,
        gender: data.gender,
        birthDate: data.birthDate ? new Date(data.birthDate) : undefined,
        color: data.color,
        weight: data.weight ? parseFloat(data.weight) : null,
        height: data.height ? parseFloat(data.height) : null,
        pedigree: data.pedigree,
        achievements: data.achievements,
        description: data.description,
        isForSale: data.isForSale,
        price: data.price ? parseFloat(data.price) : null,
        isPublished: data.isPublished,
      }
    });
    
    return NextResponse.json(updatedDog);
  } catch (error) {
    console.error('Ошибка при обновлении информации о собаке:', error);
    return NextResponse.json(
      { error: 'Ошибка при обновлении информации о собаке' },
      { status: 500 }
    );
  }
}

// DELETE /api/dogs/[slug] - Удаление собаки (защищенный маршрут)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Проверка аутентификации
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Не авторизован' },
        { status: 401 }
      );
    }
    
    const slug = params.slug;
    
    // Проверяем, существует ли собака
    const existingDog = await prisma.dog.findUnique({
      where: { slug }
    });
    
    if (!existingDog) {
      return NextResponse.json(
        { error: 'Собака не найдена' },
        { status: 404 }
      );
    }
    
    // Удаляем собаку
    await prisma.dog.delete({
      where: { slug }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Ошибка при удалении собаки:', error);
    return NextResponse.json(
      { error: 'Ошибка при удалении собаки' },
      { status: 500 }
    );
  }
}
