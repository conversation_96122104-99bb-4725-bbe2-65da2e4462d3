'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Textarea from '@/components/ui/Textarea';
import FileUpload from '@/components/ui/FileUpload';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

interface Settings {
  [key: string]: string;
}

const settingsSchema = z.object({
  siteName: z.string().min(1, 'Название сайта обязательно'),
  siteDescription: z.string().optional(),
  contactEmail: z.string().email('Введите корректный email').optional().nullable(),
  contactPhone: z.string().optional().nullable(),
  address: z.string().optional().nullable(),
  facebookUrl: z.string().url('Введите корректный URL').optional().nullable(),
  instagramUrl: z.string().url('Введите корректный URL').optional().nullable(),
  vkUrl: z.string().url('Введите корректный URL').optional().nullable(),
  youtubeUrl: z.string().url('Введите корректный URL').optional().nullable(),
  metaTitle: z.string().optional().nullable(),
  metaDescription: z.string().optional().nullable(),
  metaKeywords: z.string().optional().nullable(),
});

type SettingsFormData = z.infer<typeof settingsSchema>;

export default function SettingsPage() {
  const [settings, setSettings] = useState<Settings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [logo, setLogo] = useState<File | null>(null);
  const [favicon, setFavicon] = useState<File | null>(null);
  const [heroImage, setHeroImage] = useState<File | null>(null);
  const [aboutImage, setAboutImage] = useState<File | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema),
  });

  useEffect(() => {
    const fetchSettings = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/settings');

        if (!response.ok) {
          throw new Error('Ошибка при загрузке данных');
        }

        const data: Settings = await response.json();
        setSettings(data);

        // Преобразуем данные для формы
        reset({
          siteName: data.siteName || 'Питомник собак',
          siteDescription: data.siteDescription || '',
          contactEmail: data.contactEmail || '',
          contactPhone: data.contactPhone || '',
          address: data.address || '',
          facebookUrl: data.facebookUrl || '',
          instagramUrl: data.instagramUrl || '',
          vkUrl: data.vkUrl || '',
          youtubeUrl: data.youtubeUrl || '',
          metaTitle: data.metaTitle || '',
          metaDescription: data.metaDescription || '',
          metaKeywords: data.metaKeywords || '',
        });
      } catch (err) {
        setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
        console.error('Ошибка при загрузке настроек:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, [reset]);

  const onSubmit = async (data: SettingsFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Отправляем запрос на обновление настроек
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Ошибка при обновлении настроек');
      }

      // Если есть новый логотип, загружаем его
      if (logo) {
        const formData = new FormData();
        formData.append('file', logo);
        formData.append('type', 'logo');

        const logoResponse = await fetch('/api/settings/upload', {
          method: 'POST',
          body: formData,
        });

        if (!logoResponse.ok) {
          console.error('Ошибка при загрузке логотипа:', await logoResponse.text());
        }
      }

      // Если есть новый фавикон, загружаем его
      if (favicon) {
        const formData = new FormData();
        formData.append('file', favicon);
        formData.append('type', 'favicon');

        const faviconResponse = await fetch('/api/settings/upload', {
          method: 'POST',
          body: formData,
        });

        if (!faviconResponse.ok) {
          console.error('Ошибка при загрузке фавикона:', await faviconResponse.text());
        }
      }

      setSuccess('Настройки успешно обновлены');

      // Обновляем данные на странице
      const updatedSettingsResponse = await fetch('/api/settings');
      if (updatedSettingsResponse.ok) {
        const updatedSettings = await updatedSettingsResponse.json();
        setSettings(updatedSettings);
      }

      // Если есть новое изображение для hero-секции, загружаем его
      if (heroImage) {
        const formData = new FormData();
        formData.append('file', heroImage);
        formData.append('type', 'heroImage');

        const heroImageResponse = await fetch('/api/settings/upload', {
          method: 'POST',
          body: formData,
        });

        if (!heroImageResponse.ok) {
          console.error('Ошибка при загрузке изображения для hero-секции:', await heroImageResponse.text());
        }
      }

      // Если есть новое изображение для раздела "О нас", загружаем его
      if (aboutImage) {
        const formData = new FormData();
        formData.append('file', aboutImage);
        formData.append('type', 'aboutImage');

        const aboutImageResponse = await fetch('/api/settings/upload', {
          method: 'POST',
          body: formData,
        });

        if (!aboutImageResponse.ok) {
          console.error('Ошибка при загрузке изображения для раздела "О нас":', await aboutImageResponse.text());
        }
      }

      // Сбрасываем состояние
      setLogo(null);
      setFavicon(null);
      setHeroImage(null);
      setAboutImage(null);
    } catch (err) {
      console.error('Ошибка при обновлении настроек:', err);
      setError('Произошла ошибка при обновлении настроек. Пожалуйста, попробуйте позже.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLogoChange = (files: File[]) => {
    if (files.length > 0) {
      setLogo(files[0]);
    } else {
      setLogo(null);
    }
  };

  const handleFaviconChange = (files: File[]) => {
    if (files.length > 0) {
      setFavicon(files[0]);
    } else {
      setFavicon(null);
    }
  };

  const handleHeroImageChange = (files: File[]) => {
    if (files.length > 0) {
      setHeroImage(files[0]);
    } else {
      setHeroImage(null);
    }
  };

  const handleAboutImageChange = (files: File[]) => {
    if (files.length > 0) {
      setAboutImage(files[0]);
    } else {
      setAboutImage(null);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Загрузка...">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Настройки сайта">
      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Основные настройки</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Название сайта"
              {...register('siteName')}
              error={errors.siteName?.message}
              fullWidth
            />
            <div className="md:col-span-2">
              <Textarea
                label="Описание сайта"
                {...register('siteDescription')}
                error={errors.siteDescription?.message}
                rows={3}
                fullWidth
              />
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Логотип и фавикон</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p className="text-sm font-medium text-gray-700 mb-2">Логотип</p>
              {settings?.logo && (
                <div className="mb-4">
                  <p className="text-sm text-gray-500 mb-2">Текущий логотип:</p>
                  <img
                    src={settings.logo}
                    alt="Логотип"
                    className="max-h-16 mb-2"
                  />
                </div>
              )}
              <FileUpload
                label="Загрузить новый логотип"
                accept="image/*"
                onChange={handleLogoChange}
                fullWidth
              />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700 mb-2">Фавикон</p>
              {settings?.favicon && (
                <div className="mb-4">
                  <p className="text-sm text-gray-500 mb-2">Текущий фавикон:</p>
                  <img
                    src={settings.favicon}
                    alt="Фавикон"
                    className="max-h-16 mb-2"
                  />
                </div>
              )}
              <FileUpload
                label="Загрузить новый фавикон (рекомендуется .ico или .png)"
                accept="image/x-icon,image/png"
                onChange={handleFaviconChange}
                fullWidth
              />
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Изображения для главной страницы</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p className="text-sm font-medium text-gray-700 mb-2">Изображение для Hero-секции</p>
              {settings?.heroImage && (
                <div className="mb-4">
                  <p className="text-sm text-gray-500 mb-2">Текущее изображение:</p>
                  <div className="relative h-40 w-full rounded-lg overflow-hidden">
                    <img
                      src={settings.heroImage}
                      alt="Hero-изображение"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              )}
              <FileUpload
                label="Загрузить новое изображение для Hero-секции"
                accept="image/*"
                onChange={handleHeroImageChange}
                fullWidth
              />
              <p className="text-xs text-gray-500 mt-1">
                Рекомендуемый размер: 1920x1080 пикселей
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700 mb-2">Изображение для раздела "О нас"</p>
              {settings?.aboutImage && (
                <div className="mb-4">
                  <p className="text-sm text-gray-500 mb-2">Текущее изображение:</p>
                  <div className="relative h-40 w-full rounded-lg overflow-hidden">
                    <img
                      src={settings.aboutImage}
                      alt="Изображение о нас"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              )}
              <FileUpload
                label="Загрузить новое изображение для раздела 'О нас'"
                accept="image/*"
                onChange={handleAboutImageChange}
                fullWidth
              />
              <p className="text-xs text-gray-500 mt-1">
                Рекомендуемый размер: 800x600 пикселей
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Контактная информация</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Email"
              type="email"
              {...register('contactEmail')}
              error={errors.contactEmail?.message}
              fullWidth
            />
            <Input
              label="Телефон"
              {...register('contactPhone')}
              error={errors.contactPhone?.message}
              fullWidth
            />
            <div className="md:col-span-2">
              <Textarea
                label="Адрес"
                {...register('address')}
                error={errors.address?.message}
                rows={2}
                fullWidth
              />
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Социальные сети</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Facebook"
              {...register('facebookUrl')}
              error={errors.facebookUrl?.message}
              placeholder="https://facebook.com/..."
              fullWidth
            />
            <Input
              label="Instagram"
              {...register('instagramUrl')}
              error={errors.instagramUrl?.message}
              placeholder="https://instagram.com/..."
              fullWidth
            />
            <Input
              label="ВКонтакте"
              {...register('vkUrl')}
              error={errors.vkUrl?.message}
              placeholder="https://vk.com/..."
              fullWidth
            />
            <Input
              label="YouTube"
              {...register('youtubeUrl')}
              error={errors.youtubeUrl?.message}
              placeholder="https://youtube.com/..."
              fullWidth
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">SEO настройки</h2>
          <div className="grid grid-cols-1 gap-6">
            <Input
              label="Meta Title"
              {...register('metaTitle')}
              error={errors.metaTitle?.message}
              fullWidth
            />
            <Textarea
              label="Meta Description"
              {...register('metaDescription')}
              error={errors.metaDescription?.message}
              rows={2}
              fullWidth
            />
            <Textarea
              label="Meta Keywords"
              {...register('metaKeywords')}
              error={errors.metaKeywords?.message}
              rows={2}
              placeholder="ключевое слово 1, ключевое слово 2, ..."
              fullWidth
            />
          </div>
        </div>

        <div className="flex justify-end">
          <Button type="submit" isLoading={isSubmitting}>
            Сохранить настройки
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
