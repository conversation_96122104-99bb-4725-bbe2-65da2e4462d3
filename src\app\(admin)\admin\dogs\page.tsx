'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Table from '@/components/ui/Table';
import Pagination from '@/components/ui/Pagination';
import Alert from '@/components/ui/Alert';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ReactNode } from 'react';

interface Column<T> {
  header: string;
  accessor: keyof T | ((item: T) => ReactNode);
  className?: string;
}

interface Dog {
  id: string;
  name: string;
  breed: string;
  gender: 'MALE' | 'FEMALE';
  birthDate: string;
  isForSale: boolean;
  price: number | null;
  isPublished: boolean;
  slug: string;
  photos: {
    id: string;
    url: string;
    isMain: boolean;
  }[];
}

export default function DogsPage() {
  const [dogs, setDogs] = useState<Dog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const router = useRouter();

  const fetchDogs = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/dogs');
      
      if (!response.ok) {
        throw new Error('Ошибка при загрузке данных');
      }
      
      const data = await response.json();
      setDogs(data);
      setTotalPages(Math.ceil(data.length / 10)); // Предполагаем 10 элементов на странице
    } catch (err) {
      setError('Произошла ошибка при загрузке данных. Пожалуйста, попробуйте позже.');
      console.error('Ошибка при загрузке собак:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDogs();
  }, []);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRowClick = (dog: Dog) => {
    router.push(`/admin/dogs/${dog.slug}`);
  };

  const getGenderText = (gender: 'MALE' | 'FEMALE') => {
    return gender === 'MALE' ? 'Кобель' : 'Сука';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU');
  };

  const formatPrice = (price: number | null) => {
    if (price === null) return '-';
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const columns: Column<Dog>[] = [
    {
      header: 'Фото',
      accessor: (dog: Dog) => {
        const mainPhoto = dog.photos?.find(photo => photo.isMain);
        return mainPhoto ? (
          <img
            src={mainPhoto.url}
            alt={dog.name}
            className="h-12 w-12 object-cover rounded-full"
          />
        ) : (
          <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        );
      },
      className: 'w-20',
    },
    {
      header: 'Имя',
      accessor: 'name',
    },
    {
      header: 'Порода',
      accessor: 'breed',
    },
    {
      header: 'Пол',
      accessor: (dog: Dog) => getGenderText(dog.gender),
    },
    {
      header: 'Дата рождения',
      accessor: (dog: Dog) => formatDate(dog.birthDate),
    },
    {
      header: 'На продажу',
      accessor: (dog: Dog) => (dog.isForSale ? 'Да' : 'Нет'),
    },
    {
      header: 'Цена',
      accessor: (dog: Dog) => formatPrice(dog.price),
    },
    {
      header: 'Опубликовано',
      accessor: (dog: Dog) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            dog.isPublished
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {dog.isPublished ? 'Да' : 'Нет'}
        </span>
      ),
    },
    {
      header: 'Действия',
      accessor: (dog: Dog) => (
        <div className="flex space-x-2">
          <Link href={`/admin/dogs/${dog.slug}/edit`}>
            <Button variant="secondary" size="sm">
              Редактировать
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  // Пагинация на клиентской стороне
  const paginatedDogs = dogs.slice((currentPage - 1) * 10, currentPage * 10);

  return (
    <AdminLayout title="Управление собаками">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <p className="text-gray-500">
            Всего собак: {dogs.length}
          </p>
        </div>
        <Link href="/admin/dogs/create">
          <Button>Добавить собаку</Button>
        </Link>
      </div>

      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      <Table
        columns={columns}
        data={paginatedDogs}
        keyExtractor={(dog) => dog.id}
        onRowClick={handleRowClick}
        isLoading={isLoading}
        emptyMessage="Собаки не найдены"
      />

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />
    </AdminLayout>
  );
}
