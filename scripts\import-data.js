const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const zlib = require('zlib');

const prisma = new PrismaClient();

async function importData(backupFile) {
  try {
    console.log('🚀 Начинаем импорт данных...');

    if (!backupFile) {
      console.error('❌ Укажите файл для импорта');
      console.log('Использование: node scripts/import-data.js <путь_к_файлу>');
      process.exit(1);
    }

    if (!fs.existsSync(backupFile)) {
      console.error(`❌ Файл не найден: ${backupFile}`);
      process.exit(1);
    }

    let data;

    // Чтение файла (обычный или сжатый)
    if (backupFile.endsWith('.gz')) {
      console.log('📦 Распаковка сжатого файла...');
      const compressed = fs.readFileSync(backupFile);
      const decompressed = zlib.gunzipSync(compressed);
      data = JSON.parse(decompressed.toString());
    } else {
      console.log('📖 Чтение файла...');
      const fileContent = fs.readFileSync(backupFile, 'utf8');
      data = JSON.parse(fileContent);
    }

    console.log(`📅 Дата создания бэкапа: ${data.timestamp}`);
    console.log(`🔢 Версия: ${data.version}`);

    // Очистка существующих данных (опционально)
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const clearData = await new Promise((resolve) => {
      readline.question('🗑️ Очистить существующие данные перед импортом? (y/N): ', (answer) => {
        readline.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });

    if (clearData) {
      console.log('🗑️ Очистка существующих данных...');
      
      // Удаление в правильном порядке (учитывая связи)
      await prisma.inquiry.deleteMany();
      await prisma.photo.deleteMany();
      await prisma.puppy.deleteMany();
      await prisma.breeding.deleteMany();
      await prisma.gallery.deleteMany();
      await prisma.news.deleteMany();
      await prisma.heroSlide.deleteMany();
      await prisma.dog.deleteMany();
      await prisma.settings.deleteMany();
      await prisma.user.deleteMany();
      
      console.log('✅ Существующие данные очищены');
    }

    // Импорт пользователей
    if (data.data.users && data.data.users.length > 0) {
      console.log('📥 Импорт пользователей...');
      for (const user of data.data.users) {
        await prisma.user.create({
          data: {
            id: user.id,
            email: user.email,
            name: user.name,
            password: user.password,
            role: user.role,
            createdAt: new Date(user.createdAt),
            updatedAt: new Date(user.updatedAt)
          }
        });
      }
      console.log(`✅ Импортировано ${data.data.users.length} пользователей`);
    }

    // Импорт настроек
    if (data.data.settings && data.data.settings.length > 0) {
      console.log('📥 Импорт настроек...');
      for (const setting of data.data.settings) {
        await prisma.settings.create({
          data: {
            id: setting.id,
            key: setting.key,
            value: setting.value,
            description: setting.description,
            createdAt: new Date(setting.createdAt),
            updatedAt: new Date(setting.updatedAt)
          }
        });
      }
      console.log(`✅ Импортировано ${data.data.settings.length} настроек`);
    }

    // Импорт собак
    if (data.data.dogs && data.data.dogs.length > 0) {
      console.log('📥 Импорт собак...');
      for (const dog of data.data.dogs) {
        const createdDog = await prisma.dog.create({
          data: {
            id: dog.id,
            name: dog.name,
            breed: dog.breed,
            gender: dog.gender,
            birthDate: new Date(dog.birthDate),
            color: dog.color,
            weight: dog.weight,
            height: dog.height,
            pedigree: dog.pedigree,
            achievements: dog.achievements,
            description: dog.description,
            slug: dog.slug,
            isForSale: dog.isForSale,
            price: dog.price,
            isPublished: dog.isPublished,
            isGraduate: dog.isGraduate,
            createdAt: new Date(dog.createdAt),
            updatedAt: new Date(dog.updatedAt)
          }
        });

        // Импорт фотографий собак
        if (dog.photos && dog.photos.length > 0) {
          for (const photo of dog.photos) {
            await prisma.photo.create({
              data: {
                id: photo.id,
                url: photo.url,
                alt: photo.alt,
                isMain: photo.isMain,
                order: photo.order,
                dogId: createdDog.id,
                createdAt: new Date(photo.createdAt),
                updatedAt: new Date(photo.updatedAt)
              }
            });
          }
        }
      }
      console.log(`✅ Импортировано ${data.data.dogs.length} собак`);
    }

    // Импорт вязок
    if (data.data.breedings && data.data.breedings.length > 0) {
      console.log('📥 Импорт вязок...');
      for (const breeding of data.data.breedings) {
        await prisma.breeding.create({
          data: {
            id: breeding.id,
            fatherId: breeding.fatherId,
            motherId: breeding.motherId,
            breedingDate: new Date(breeding.breedingDate),
            expectedDate: breeding.expectedDate ? new Date(breeding.expectedDate) : null,
            actualDate: breeding.actualDate ? new Date(breeding.actualDate) : null,
            notes: breeding.notes,
            status: breeding.status,
            createdAt: new Date(breeding.createdAt),
            updatedAt: new Date(breeding.updatedAt)
          }
        });
      }
      console.log(`✅ Импортировано ${data.data.breedings.length} вязок`);
    }

    // Импорт щенков
    if (data.data.puppies && data.data.puppies.length > 0) {
      console.log('📥 Импорт щенков...');
      for (const puppy of data.data.puppies) {
        const createdPuppy = await prisma.puppy.create({
          data: {
            id: puppy.id,
            name: puppy.name,
            breed: puppy.breed,
            gender: puppy.gender,
            birthDate: new Date(puppy.birthDate),
            color: puppy.color,
            price: puppy.price,
            status: puppy.status,
            description: puppy.description,
            slug: puppy.slug,
            isPublished: puppy.isPublished,
            breedingId: puppy.breedingId,
            createdAt: new Date(puppy.createdAt),
            updatedAt: new Date(puppy.updatedAt)
          }
        });

        // Импорт фотографий щенков
        if (puppy.photos && puppy.photos.length > 0) {
          for (const photo of puppy.photos) {
            await prisma.photo.create({
              data: {
                id: photo.id,
                url: photo.url,
                alt: photo.alt,
                isMain: photo.isMain,
                order: photo.order,
                puppyId: createdPuppy.id,
                createdAt: new Date(photo.createdAt),
                updatedAt: new Date(photo.updatedAt)
              }
            });
          }
        }
      }
      console.log(`✅ Импортировано ${data.data.puppies.length} щенков`);
    }

    // Импорт новостей
    if (data.data.news && data.data.news.length > 0) {
      console.log('📥 Импорт новостей...');
      for (const news of data.data.news) {
        await prisma.news.create({
          data: {
            id: news.id,
            title: news.title,
            content: news.content,
            excerpt: news.excerpt,
            slug: news.slug,
            featuredImage: news.featuredImage,
            isPublished: news.isPublished,
            publishedAt: news.publishedAt ? new Date(news.publishedAt) : null,
            authorId: news.authorId,
            createdAt: new Date(news.createdAt),
            updatedAt: new Date(news.updatedAt)
          }
        });
      }
      console.log(`✅ Импортировано ${data.data.news.length} новостей`);
    }

    // Импорт hero-слайдов
    if (data.data.heroSlides && data.data.heroSlides.length > 0) {
      console.log('📥 Импорт hero-слайдов...');
      for (const slide of data.data.heroSlides) {
        await prisma.heroSlide.create({
          data: {
            id: slide.id,
            title: slide.title,
            subtitle: slide.subtitle,
            imageUrl: slide.imageUrl,
            buttonText: slide.buttonText,
            buttonLink: slide.buttonLink,
            order: slide.order,
            isActive: slide.isActive,
            createdAt: new Date(slide.createdAt),
            updatedAt: new Date(slide.updatedAt)
          }
        });
      }
      console.log(`✅ Импортировано ${data.data.heroSlides.length} слайдов`);
    }

    console.log('🎉 Импорт завершен успешно!');

  } catch (error) {
    console.error('❌ Ошибка при импорте данных:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Получение аргумента командной строки
const backupFile = process.argv[2];
importData(backupFile);
