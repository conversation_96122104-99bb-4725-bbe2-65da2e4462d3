'use client';

import AdminLayout from '@/components/admin/AdminLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Textarea from '@/components/ui/Textarea';
import Select from '@/components/ui/Select';
import FileUpload from '@/components/ui/FileUpload';
import Alert from '@/components/ui/Alert';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { slugify } from '@/lib/utils';

const dogSchema = z.object({
  name: z.string().min(1, 'Имя обязательно'),
  breed: z.string().min(1, 'Порода обязательна'),
  gender: z.enum(['MALE', 'FEMALE']),
  birthDate: z.string().min(1, 'Дата рождения обязательна'),
  color: z.string().optional(),
  weight: z.string().optional(),
  height: z.string().optional(),
  pedigree: z.string().optional(),
  achievements: z.string().optional(),
  description: z.string().optional(),
  isForSale: z.boolean().default(false),
  price: z.string().optional(),
  isPublished: z.boolean().default(true),
});

type DogFormData = z.infer<typeof dogSchema>;

export default function CreateDogPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [photos, setPhotos] = useState<File[]>([]);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors },
  } = useForm<DogFormData>({
    resolver: zodResolver(dogSchema),
    defaultValues: {
      gender: 'MALE',
      isForSale: false,
      isPublished: true,
    },
  });

  const isForSale = watch('isForSale');

  const onSubmit = async (data: DogFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Создаем slug из имени собаки
      const slug = slugify(data.name);

      // Создаем объект с данными собаки
      const dogData = {
        ...data,
        slug,
        weight: data.weight ? parseFloat(data.weight) : null,
        height: data.height ? parseFloat(data.height) : null,
        price: data.price ? parseFloat(data.price) : null,
      };

      // Отправляем запрос на создание собаки
      const response = await fetch('/api/dogs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dogData),
      });

      if (!response.ok) {
        throw new Error('Ошибка при создании собаки');
      }

      const createdDog = await response.json();

      // Если есть фотографии, загружаем их
      if (photos.length > 0) {
        for (let i = 0; i < photos.length; i++) {
          const formData = new FormData();
          formData.append('file', photos[i]);
          formData.append('dogId', createdDog.id);
          formData.append('isMain', i === 0 ? 'true' : 'false');
          formData.append('order', i.toString());

          const photoResponse = await fetch('/api/photos', {
            method: 'POST',
            body: formData,
          });

          if (!photoResponse.ok) {
            console.error('Ошибка при загрузке фотографии:', await photoResponse.text());
          }
        }
      }

      setSuccess('Собака успешно создана');
      
      // Перенаправляем на страницу со списком собак
      setTimeout(() => {
        router.push('/admin/dogs');
      }, 2000);
    } catch (err) {
      console.error('Ошибка при создании собаки:', err);
      setError('Произошла ошибка при создании собаки. Пожалуйста, попробуйте позже.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePhotosChange = (files: File[]) => {
    setPhotos(files);
  };

  return (
    <AdminLayout title="Добавление новой собаки">
      {error && (
        <div className="mb-6">
          <Alert type="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </div>
      )}

      {success && (
        <div className="mb-6">
          <Alert type="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Основная информация</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Имя собаки"
              {...register('name')}
              error={errors.name?.message}
              fullWidth
            />
            <Input
              label="Порода"
              {...register('breed')}
              error={errors.breed?.message}
              fullWidth
            />
            <Controller
              name="gender"
              control={control}
              render={({ field }) => (
                <Select
                  label="Пол"
                  options={[
                    { value: 'MALE', label: 'Кобель' },
                    { value: 'FEMALE', label: 'Сука' },
                  ]}
                  {...field}
                  error={errors.gender?.message}
                  fullWidth
                />
              )}
            />
            <Input
              label="Дата рождения"
              type="date"
              {...register('birthDate')}
              error={errors.birthDate?.message}
              fullWidth
            />
            <Input
              label="Окрас"
              {...register('color')}
              error={errors.color?.message}
              fullWidth
            />
            <Input
              label="Вес (кг)"
              type="number"
              step="0.1"
              {...register('weight')}
              error={errors.weight?.message}
              fullWidth
            />
            <Input
              label="Рост (см)"
              type="number"
              step="0.1"
              {...register('height')}
              error={errors.height?.message}
              fullWidth
            />
            <Input
              label="Родословная"
              {...register('pedigree')}
              error={errors.pedigree?.message}
              fullWidth
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Дополнительная информация</h2>
          <div className="space-y-6">
            <Textarea
              label="Достижения"
              {...register('achievements')}
              error={errors.achievements?.message}
              rows={3}
              fullWidth
            />
            <Textarea
              label="Описание"
              {...register('description')}
              error={errors.description?.message}
              rows={5}
              fullWidth
            />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Продажа</h2>
          <div className="space-y-6">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isForSale"
                {...register('isForSale')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isForSale" className="ml-2 block text-sm text-gray-900">
                Выставить на продажу
              </label>
            </div>

            {isForSale && (
              <Input
                label="Цена (руб.)"
                type="number"
                {...register('price')}
                error={errors.price?.message}
                fullWidth
              />
            )}

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPublished"
                {...register('isPublished')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
                Опубликовать на сайте
              </label>
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Фотографии</h2>
          <div className="space-y-6">
            <FileUpload
              label="Загрузите фотографии собаки (первая фотография будет использоваться как основная)"
              accept="image/*"
              multiple
              onChange={handlePhotosChange}
              fullWidth
            />
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="secondary"
            onClick={() => router.push('/admin/dogs')}
          >
            Отмена
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            Создать
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
